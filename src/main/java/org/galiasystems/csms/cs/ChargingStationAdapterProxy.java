package org.galiasystems.csms.cs;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

import org.galiasystems.csms.cs.adapters.ocpp.v1_6.Ocpp16Adapter;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.Ocpp201Adapter;
import org.galiasystems.csms.management.cs.ChargingStationAdapter;
import org.galiasystems.csms.management.cs.ChargingStationManager;
import org.galiasystems.csms.management.model.ChargingStation;
import org.galiasystems.csms.management.model.ChargingStationVariable;
import org.galiasystems.csms.management.model.EvseTransactionEvent;
import org.galiasystems.csms.management.model.IdToken;
import org.galiasystems.csms.management.model.MeterValue;
import org.galiasystems.csms.management.model.enums.AvailabilityStatus;
import org.galiasystems.csms.management.model.enums.ChargingStationStatus;
import org.galiasystems.csms.management.model.enums.ConnectorStatus;
import org.galiasystems.csms.management.model.enums.EvseStatus;
import org.galiasystems.csms.management.model.enums.ReportType;
import org.galiasystems.csms.management.model.enums.ResetType;
import org.galiasystems.csms.management.types.*;

import io.smallrye.mutiny.Uni;

import org.galiasystems.csms.management.types.enums.AuthorizationStatus;
import org.galiasystems.csms.management.types.enums.MessageTrigger;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ApplicationScoped
public final class ChargingStationAdapterProxy implements ChargingStationAdapter {

    private final Map<Long, ChargingStationAdapterType> chargingStationAdapters = new HashMap<>();

    @Inject
    private ChargingStationManager chargingStationManager;

    @Inject
    private Ocpp201Adapter ocpp201Adapter;

    @Inject
    private Ocpp16Adapter ocpp16Adapter;

	////////////////////////////////////////////////////////////////
	/// Connection handler functions
	////////////////////////////////////////////////////////////////

    public Uni<ChargingStation> adapterConnectionOpened(final ChargingStationAdapterType chargingStationAdapterType,
                                                        final long chargingStationId) {

        this.chargingStationAdapters.put(chargingStationId, chargingStationAdapterType);

        return this.chargingStationManager.chargingStationConnected(chargingStationId, chargingStationAdapterType);
    }

    public Uni<ChargingStation> adapterConnectionClosed(final long chargingStationId) {

    	this.chargingStationAdapters.remove(chargingStationId);

        return this.chargingStationManager.chargingStationDown(chargingStationId);
    }
    
	////////////////////////////////////////////////////////////////
	/// Message handler functions from CS to CSMS
	////////////////////////////////////////////////////////////////

    public Uni<ChargingStation> chargingStationBooted(final ChargingStation chargingStation) {
        return this.chargingStationManager.chargingStationBooted(chargingStation);
    }

    public Uni<Void> reportResponseReceived(final long chargingStationId, final long requestId,
    		final ReportRequestResult reportResult) {
    	
    	return this.chargingStationManager.reportResponseReceived(chargingStationId, requestId, reportResult)
    				.replaceWithVoid();
    }

    public Uni<Void> reportDataReceived(final long chargingStationId, final int reportRequestId,
    		final boolean finalData, final Collection<ChargingStationVariable> chargingStationVariables) {
    	
    	return this.chargingStationManager.reportDataReceived(chargingStationId, reportRequestId, finalData,
    			chargingStationVariables);
    }
    
    public Uni<Void> chargingStationStatusReceived(final long chargingStationId, final ChargingStationStatus chargingStationStatus,
    		final ZonedDateTime lastStatusUpdate, final String errorCode, final String errorInfo, final String vendorImplementationId,
			final String vendorErrorCode) {
		
    	return this.chargingStationManager.chargingStationStatusReceived(chargingStationId, chargingStationStatus, lastStatusUpdate,
    			errorCode, errorInfo, vendorImplementationId, vendorErrorCode);
	}
    
    public Uni<Void> evseStatusReceived(final long chargingStationId, final int evseId, final EvseStatus evseStatus) {
    	
    	return this.chargingStationManager.evseStatusReceived(chargingStationId, evseId, evseStatus);
    }

    public Uni<Void> connectorStatusReceived(final long chargingStationId, final int evseId, final int connectorId,
    		final ConnectorStatus connectorStatus, final ZonedDateTime lastStatusUpdate, final String errorCode, final String errorInfo, 
    		final String vendorImplementationId, final String vendorErrorCode) {
    	
    	return this.chargingStationManager.connectorStatusReceived(chargingStationId, evseId, connectorId,
    			connectorStatus, lastStatusUpdate, errorCode, errorInfo, vendorImplementationId, vendorErrorCode);
    }
    
	public Uni<Void> setVariableResultReceived(final long chargingStationId, final List<SetVariableResult> setVariableResults) {

		return this.chargingStationManager.setVariableResultReceived(chargingStationId, setVariableResults);
	}
    
    public Uni<Void> getVariableResultReceived(final long chargingStationId, final List<GetVariableResult> getVariableResults) {
    	
    	return this.chargingStationManager.getVariableResultReceived(chargingStationId, getVariableResults);
    }
    
    public Uni<Void> changeAvailabilityResultReceived(final long chargingStationId,
			final ChangeAvailabilityResult changeAvailabilityResult) {
		
    	return this.chargingStationManager.changeAvailabilityResultReceived(chargingStationId, changeAvailabilityResult);
	}
    
    public Uni<Void> resetResultReceived(final long chargingStationId, final ResetResult resetResult) {
    	
    	return this.chargingStationManager.resetResultReceived(chargingStationId, resetResult);
    }
    
    public Uni<AuthorizationStatus> authorizeRequestReceived(final long chargingStationId, final String idToken) {
    	return this.chargingStationManager.authorizeRequestReceived(chargingStationId, idToken);
	}
    
	public Uni<TransactionEventResult> transactionEventReceived(final long chargingStationId, final Integer chargingStationEvseId, 
			final Integer evseConnectorId, final String evseTransactionId, final EvseTransactionEvent transactionEvent) {
		
		return this.chargingStationManager.transactionEventReceived(chargingStationId, chargingStationEvseId, 
				evseConnectorId, evseTransactionId, transactionEvent);
	}

    public Uni<Void> requestStartTransactionResultReceived(final long chargingStationId, 
    													  final RequestStartTransactionResult requestStartTransactionResult) {
        return this.chargingStationManager.requestStartTransactionResultReceived(chargingStationId, requestStartTransactionResult);
    }

    public Uni<Void> requestStopTransactionResultReceived(final long chargingStationId, 
    													 final RequestStopTransactionResult requestStopTransactionResult) {
        return this.chargingStationManager.requestStopTransactionResultReceived(chargingStationId, requestStopTransactionResult);
    }


    public Uni<Void> meterValueReceived(final long chargingStationId, final Integer chargingStationEvseId, 
    		final Collection<MeterValue> meterValues) {
        return this.chargingStationManager.meterValueReceived(chargingStationId, chargingStationEvseId, meterValues);
    }

    public Uni<Void> getDiagnosticsResultReceived(final long chargingStationId,
                                                  final GetDiagnosticsResult getDiagnosticsResult) {

        return this.chargingStationManager.getDiagnosticsResultReceived(chargingStationId, getDiagnosticsResult);
    }

    public Uni<Void> triggerMessageReceived(final long chargingStationId, final TriggerMessageResult triggerMessageResult) {

        return this.chargingStationManager.triggerMessageReceived(chargingStationId, triggerMessageResult);
    }

    ////////////////////////////////////////////////////////////////
    /// Message handler functions from CSMS to CS
    ////////////////////////////////////////////////////////////////

    @Override
    public Uni<Void> executeReport(final long chargingStationId, final long requestId, final ReportType type) {
        final var chargingStationAdapter = getAdapter(chargingStationId);
        return chargingStationAdapter.executeReport(chargingStationId, requestId, type);
    }
    
    @Override
    public Uni<List<SetVariableResult>> setVariables(final long chargingStationId, final List<VariableValueToSet> variableValuesToSet) {
        final var chargingStationAdapter = getAdapter(chargingStationId);
        return chargingStationAdapter.setVariables(chargingStationId, variableValuesToSet);
    }
    
	@Override
	public Uni<List<GetVariableResult>> getVariables(final long chargingStationId, final List<VariableValueToGet> variableValuesToGet) {
		final var chargingStationAdapter = getAdapter(chargingStationId);
		return chargingStationAdapter.getVariables(chargingStationId, variableValuesToGet);
	}
	
	@Override
	public Uni<ChangeAvailabilityResult> changeAvailability(final long chargingStationId, final Integer evseId, final Integer connectorId,
			final AvailabilityStatus availabilityStatus) {
		final var chargingStationAdapter = getAdapter(chargingStationId);
		return chargingStationAdapter.changeAvailability(chargingStationId, evseId, connectorId, availabilityStatus);
	}
	
	@Override
	public Uni<ResetResult> reset(final long chargingStationId, final ResetType resetType, final Integer evseId) {
		final var chargingStationAdapter = getAdapter(chargingStationId);
		return chargingStationAdapter.reset(chargingStationId, resetType, evseId);
	}

    @Override
    public Uni<RequestStartTransactionResult> requestStartTransaction(final long chargingStationId, final Integer evseId, final long remoteStartId, 
    		final IdToken idToken) {
        final var chargingStationAdapter = getAdapter(chargingStationId);
        return chargingStationAdapter.requestStartTransaction(chargingStationId, evseId, remoteStartId, idToken);
    }

    @Override
    public Uni<RequestStopTransactionResult> requestStopTransaction(final long chargingStationId, final String transactionId) {
        final var chargingStationAdapter = getAdapter(chargingStationId);
        return chargingStationAdapter.requestStopTransaction(chargingStationId, transactionId);
    }

    public Uni<Long> getNextEvseTransactionId() {
        return chargingStationManager.getNextEvseTransactionId();
    }

    @Override
    public Uni<GetDiagnosticsResult> getDiagnostics(final long chargingStationId, final String location,
                                                    final Integer retries, final Integer retryInterval,
                                                    final ZonedDateTime startTime, final ZonedDateTime stopTime) {
        final var chargingStationAdapter = getAdapter(chargingStationId);
        return chargingStationAdapter.getDiagnostics(chargingStationId, location, retries,
                retryInterval, startTime, stopTime);
    }

    @Override
    public Uni<TriggerMessageResult> triggerMessage(long chargingStationId, MessageTrigger messageTrigger, Integer connectorId) {
        final var chargingStationAdapter = getAdapter(chargingStationId);
        return chargingStationAdapter.triggerMessage(chargingStationId, messageTrigger, connectorId);
    }

    private ChargingStationAdapter getAdapter(final long chargingStationId) {
        final var adapterType = this.chargingStationAdapters.get(chargingStationId);

        if (adapterType == null) {
            throw new RuntimeException("Adapter for '" + chargingStationId + "' Charging Station not found!");
        }

        switch (adapterType) {
            case OCPP_2_0_1: {
                return this.ocpp201Adapter;
            }
            case OCPP_1_6: {
                return this.ocpp16Adapter;
            }
            default: {
                throw new IllegalStateException("Unknown adapter type: " + adapterType);
            }
        }
    }
}